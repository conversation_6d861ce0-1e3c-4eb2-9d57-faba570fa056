from datetime import timedelta
from enum import Enum

# REST API Endpoints
# Spot End Points
AMBERDATA_SPOT_REST_OHLCV_ENDPOINT = "https://api.amberdata.com/markets/spot/ohlcv/"
AMBERDATA_SPOT_REST_LEGACY_OHLCV_ENDPOINT = "https://api.amberdata.com/market/spot/ohlcv/"
AMBERDATA_SPOT_REST_TRADES_ENDPOINT = "https://api.amberdata.com/markets/spot/trades/"
AMBERDATA_SPOT_REST_PRICES_ENDPOINT = "https://api.amberdata.com/market/spot/prices/"
AMBERDATA_SPOT_REST_EXCHANGES_ENDPOINT = "https://api.amberdata.com/markets/spot/exchanges/information/"
AMBERDATA_SPOT_REST_PAIRS_ENDPOINT = "https://api.amberdata.com/market/spot/prices/pairs/information/"
AMBERDATA_SPOT_REST_EXCHANGES_REFERENCE_ENDPOINT = "https://api.amberdata.com/markets/spot/exchanges/reference/"
AMBERDATA_SPOT_REST_REFERENCE_RATES_ENDPOINT = "https://api.amberdata.com/markets/spot/reference-rates/"
AMBERDATA_SPOT_REST_TICKERS_ENDPOINT = "https://api.amberdata.com/markets/spot/tickers/"
AMBERDATA_SPOT_REST_TWAP_ENDPOINT = "https://api.amberdata.com/market/spot/twap/"
AMBERDATA_SPOT_REST_ORDER_BOOK_EVENTS_ENDPOINT = "https://api.amberdata.com/markets/spot/order-book-events/"
AMBERDATA_SPOT_REST_ORDER_BOOK_SNAPSHOTS_ENDPOINT = "https://api.amberdata.com/markets/spot/order-book-snapshots/"
AMBERDATA_SPOT_REST_VWAP_ENDPOINT = "https://api.amberdata.com/market/spot/vwap/"

# Futures End Points
AMBERDATA_FUTURES_REST_EXCHANGES_ENDPOINT = "https://api.amberdata.com//markets/futures/exchanges/"
AMBERDATA_FUTURES_REST_FUNDING_RATES_ENDPOINT = "https://api.amberdata.com/markets/futures/funding-rates/"
AMBERDATA_FUTURES_REST_BATCH_FUNDING_RATES_ENDPOINT = "https://api.amberdata.com/market/futures/funding-rates/"
AMBERDATA_FUTURES_REST_INSURANCE_FUNDS_ENDPOINT = "https://api.amberdata.com/markets/futures/insurance-fund/"
AMBERDATA_FUTURES_REST_LIQUIDATIONS_ENDPOINT = "https://api.amberdata.com/markets/futures/liquidations/"
AMBERDATA_FUTURES_REST_LONG_SHORT_RATIO_ENDPOINT = "https://api.amberdata.com/markets/futures/long-short-ratio/"
AMBERDATA_FUTURES_REST_OHLCV_ENDPOINT = "https://api.amberdata.com/markets/futures/ohlcv/"
AMBERDATA_FUTURES_REST_BATCH_OHLCV_ENDPOINT = "https://api.amberdata.com/market/futures/ohlcv/"
AMBERDATA_FUTURES_REST_OPEN_INTEREST_ENDPOINT = "https://api.amberdata.com/markets/futures/open-interest/"
AMBERDATA_FUTURES_REST_BATCH_OPEN_INTEREST_ENDPOINT = "https://api.amberdata.com/market/futures/open-interest/"
AMBERDATA_FUTURES_REST_ORDER_BOOK_SNAPSHOTS_ENDPOINT = "https://api.amberdata.com/markets/futures/order-book-snapshots/"
AMBERDATA_FUTURES_REST_ORDER_BOOK_EVENTS_ENDPOINT = "https://api.amberdata.com/markets/futures/order-book-events/"
AMBERDATA_FUTURES_REST_TICKERS_ENDPOINT = "https://api.amberdata.com/markets/futures/tickers/"
AMBERDATA_FUTURES_REST_TRADES_ENDPOINT = "https://api.amberdata.com/markets/futures/trades/"

# Swaps End Points
AMBERDATA_SWAPS_REST_BATCH_FUNDING_RATES_ENDPOINT = "https://api.amberdata.com/market/swaps/funding-rates/"
AMBERDATA_SWAPS_REST_BATCH_OHLCV_ENDPOINT = "https://api.amberdata.com/market/swaps/ohlcv/"
AMBERDATA_SWAPS_REST_BATCH_OPEN_INTEREST_ENDPOINT = "https://api.amberdata.com/market/swaps/open-interest/"

# Defi API Endpoints
AMBERDATA_DEFI_REST_LENDING_PROTOCOLS_INFORMATION_ENDPOINT = "https://api.amberdata.com/defi/lending/protocols/information"
AMBERDATA_DEFI_REST_LENDING_ASSETS_INFORMATION_ENDPOINT = "https://api.amberdata.com/defi/lending/assets/information"
AMBERDATA_DEFI_REST_DEX_EXCHANGES_ENDPOINT = "https://api.amberdata.com/market/defi/dex/exchanges"
AMBERDATA_DEFI_REST_DEX_PAIRS_ENDPOINT = "https://api.amberdata.com/market/defi/dex/pairs"
AMBERDATA_DEFI_REST_PROTOCOL_LENS_ENDPOINT = "https://api.amberdata.com/defi/lending/{protocol_id}/protocol"
AMBERDATA_DEFI_REST_ASSET_LENS_ENDPOINT = "https://api.amberdata.com/defi/lending/{protocol_id}/assets/{asset}"
AMBERDATA_DEFI_REST_WALLET_LENS_ENDPOINT = "https://api.amberdata.com/defi/lending/{protocol_id}/wallets/{wallet_address}"
AMBERDATA_DEFI_REST_GOVERNANCE_LENS_ENDPOINT = "https://api.amberdata.com/defi/lending/{protocol_id}/governance"
AMBERDATA_DEFI_REST_PROTOCOL_METRICS_SUMMARY_ENDPOINT = "https://api.amberdata.com/defi/lending/{protocol_id}/metrics/summary"
AMBERDATA_DEFI_REST_ASSET_METRICS_SUMMARY_ENDPOINT = "https://api.amberdata.com/defi/lending/{protocol_id}/assets/{asset_id}/metrics/summary"
AMBERDATA_DEFI_REST_ASSETS_INFORMATION_ENDPOINT = "https://api.amberdata.com/market/defi/prices/asset/information/"
AMBERDATA_DEFI_REST_ASSET_LATEST_ENDPOINT = "https://api.amberdata.com/market/defi/prices/asset/{asset}/latest"
AMBERDATA_DEFI_REST_ASSET_HISTORICAL_ENDPOINT = "https://api.amberdata.com/market/defi/prices/asset/{asset}/historical"
AMBERDATA_DEFI_REST_PAIRS_INFORMATION_ENDPOINT = "https://api.amberdata.com/defi/prices/pairs/information"
AMBERDATA_DEFI_REST_PAIRS_LATEST_ENDPOINT = "https://api.amberdata.com/market/defi/prices/pairs/bases/{base}/quotes/{quote}/latest"
AMBERDATA_DEFI_REST_PAIRS_HISTORICAL_ENDPOINT = "https://api.amberdata.com/market/defi/prices/pairs/bases/{base}/quotes/{quote}/historical"
AMBERDATA_DEFI_REST_TWAP_ASSETS_INFORMATION_ENDPOINT = "https://api.amberdata.com/market/defi/twap/asset/information"
AMBERDATA_DEFI_REST_TWAP_ASSET_LATEST_ENDPOINT = "https://api.amberdata.com/market/defi/twap/asset/{asset}/latest"
AMBERDATA_DEFI_REST_TWAP_ASSET_HISTORICAL_ENDPOINT = "https://api.amberdata.com/market/defi/twap/asset/{asset}/historical"
AMBERDATA_DEFI_REST_TWAP_PAIRS_INFORMATION_ENDPOINT = "https://api.amberdata.com/market/defi/twap/pairs/information"
AMBERDATA_DEFI_REST_TWAP_PAIRS_LATEST_ENDPOINT = "https://api.amberdata.com/market/defi/twap/pairs/bases/{base}/quotes/{quote}/latest"
AMBERDATA_DEFI_REST_TWAP_PAIRS_HISTORICAL_ENDPOINT = "https://api.amberdata.com/market/defi/twap/pairs/bases/{base}/quotes/{quote}/historical"
AMBERDATA_DEFI_REST_VWAP_ASSETS_INFORMATION_ENDPOINT = "https://api.amberdata.com/market/defi/vwap/asset/information"
AMBERDATA_DEFI_REST_VWAP_ASSET_LATEST_ENDPOINT = "https://api.amberdata.com/market/defi/vwap/asset/{asset}/latest"
AMBERDATA_DEFI_REST_VWAP_ASSET_HISTORICAL_ENDPOINT = "https://api.amberdata.com/market/defi/vwap/asset/{asset}/historical"
AMBERDATA_DEFI_REST_VWAP_PAIRS_INFORMATION_ENDPOINT = "https://api.amberdata.com/market/defi/vwap/pairs/information"
AMBERDATA_DEFI_REST_VWAP_PAIRS_LATEST_ENDPOINT = "https://api.amberdata.com/market/defi/vwap/pairs/bases/{base}/quotes/{quote}/latest"
AMBERDATA_DEFI_REST_VWAP_PAIRS_HISTORICAL_ENDPOINT = "https://api.amberdata.com/market/defi/vwap/pairs/bases/{base}/quotes/{quote}/historical"
AMBERDATA_DEFI_REST_OHLCV_INFORMATION_ENDPOINT = "https://api.amberdata.com/defi/ohlcv/information"
AMBERDATA_DEFI_REST_OHLCV_LATEST_ENDPOINT = "https://api.amberdata.com/defi/ohlcv/{pool}/latest"
AMBERDATA_DEFI_REST_OHLCV_HISTORICAL_ENDPOINT = "https://api.amberdata.com/defi/ohlcv/{pool}/historical"
AMBERDATA_DEFI_REST_LIQUIDITY_INFORMATION_ENDPOINT = "https://api.amberdata.com/defi/liquidity/information"
AMBERDATA_DEFI_REST_LIQUIDITY_LATEST_ENDPOINT = "https://api.amberdata.com/defi/liquidity/{pool}/latest"
AMBERDATA_DEFI_REST_LIQUIDITY_HISTORICAL_ENDPOINT = "https://api.amberdata.com/defi/liquidity/{pool}/historical"
AMBERDATA_DEFI_REST_LIQUIDITY_SNAPSHOTS_ENDPOINT = "https://api.amberdata.com/defi/liquidity/pools/{poolAddress}/snapshots"
AMBERDATA_DEFI_REST_UNISWAP_V3_LIQUIDITY_DISTRIBUTION_ENDPOINT = "https://api.amberdata.com/defi/uniswap/v3/pools/{poolAddress}/liquidity-distribution"

# DeFi Liquidity Provider Return Endpoints
AMBERDATA_DEFI_REST_LIQUIDITY_PROVIDER_RETURN_SINCE_INCEPTION_ENDPOINT = "https://api.amberdata.com/defi/dex/liquidity/pools/{liquidityPoolAddress}/providers/return"
AMBERDATA_DEFI_REST_LIQUIDITY_PROVIDER_HISTORICAL_RETURN_ENDPOINT = "https://api.amberdata.com/defi/dex/liquidity/pools/{liquidityPoolAddress}/providers/return/historical"
AMBERDATA_DEFI_REST_LIQUIDITY_POOL_TOTAL_RETURN_ENDPOINT = "https://api.amberdata.com/defi/dex/liquidity/pools/{address}/return"

# DeFi Lending and Analytics Endpoints
AMBERDATA_DEFI_REST_LENDING_WALLET_POSITIONS_ENDPOINT = "https://api.amberdata.com/defi/lending/protocols/{protocolId}/wallets/{address}"
AMBERDATA_DEFI_REST_LENDING_PROFIT_LOSS_ENDPOINT = "https://api.amberdata.com/defi/lending/wallets/{walletAddress}/pnl"
AMBERDATA_DEFI_REST_IMPERMANENT_LOSS_ENDPOINT = "https://api.amberdata.com/defi/dex/wallets/{walletAddress}/impermanent-loss"
AMBERDATA_DEFI_REST_LENDING_PROTOCOL_METRICS_ENDPOINT = "https://api.amberdata.com/defi/lending/protocols/{protocolId}/metrics"
AMBERDATA_DEFI_REST_LENDING_ASSET_METRICS_ENDPOINT = "https://api.amberdata.com/defi/lending/protocols/{protocolId}/assets/{assetId}/metrics"
AMBERDATA_DEFI_REST_LENDING_PROTOCOL_LENS_ENDPOINT = "https://api.amberdata.com/defi/lending/protocols/{protocolId}/lens"
AMBERDATA_DEFI_REST_LENDING_ASSET_LENS_ENDPOINT = "https://api.amberdata.com/defi/lending/protocols/{protocolId}/assets/{asset}/lens"
AMBERDATA_DEFI_REST_LENDING_WALLET_LENS_ENDPOINT = "https://api.amberdata.com/defi/lending/protocols/{protocolId}/wallets/{walletAddress}/lens"
AMBERDATA_DEFI_REST_LENDING_GOVERNANCE_LENS_ENDPOINT = "https://api.amberdata.com/defi/lending/protocols/{protocolId}/governance/lens"
AMBERDATA_DEFI_REST_LENDING_STABLECOINS_AGGREGATE_INSIGHTS_ENDPOINT = "https://api.amberdata.com/defi/lending/stablecoins/{assetSymbol}/aggregate/insights"

# DeFi Metrics Endpoints
AMBERDATA_DEFI_REST_METRICS_EXCHANGES_LATEST_ENDPOINT = "https://api.amberdata.com/defi/metrics/exchanges/{exchange}/latest"
AMBERDATA_DEFI_REST_METRICS_EXCHANGES_HISTORICAL_ENDPOINT = "https://api.amberdata.com/defi/metrics/exchanges/{exchange}/historical"
AMBERDATA_DEFI_REST_METRICS_ASSETS_LATEST_ENDPOINT = "https://api.amberdata.com/defi/metrics/exchanges/{exchange}/assets/{asset}/latest"
AMBERDATA_DEFI_REST_METRICS_ASSETS_HISTORICAL_ENDPOINT = "https://api.amberdata.com/defi/metrics/exchanges/{exchange}/assets/{asset}/historical"
AMBERDATA_DEFI_REST_METRICS_PAIRS_LATEST_ENDPOINT = "https://api.amberdata.com/defi/metrics/exchanges/{exchange}/pairs/{pair}/latest"
AMBERDATA_DEFI_REST_METRICS_PAIRS_HISTORICAL_ENDPOINT = "https://api.amberdata.com/defi/metrics/exchanges/{exchange}/pairs/{pair}/historical"

# DEX Liquidity Positions Endpoints
AMBERDATA_DEFI_REST_DEX_LIQUIDITY_POSITIONS_PAIRS_LATEST_ENDPOINT = "https://api.amberdata.com/defi/dex/liquidity/positions/pairs/{pair}/latest"
AMBERDATA_DEFI_REST_DEX_LIQUIDITY_POSITIONS_PROVIDERS_LATEST_ENDPOINT = "https://api.amberdata.com/defi/dex/liquidity/positions/providers/{address}/latest"
AMBERDATA_DEFI_REST_DEX_LIQUIDITY_PROVIDER_EVENTS_ENDPOINT = "https://api.amberdata.com/defi/dex/liquidity/providers/{providerAddress}/events"

# DEX Lens Endpoints
AMBERDATA_DEFI_REST_DEX_PROTOCOL_LENS_ENDPOINT = "https://api.amberdata.com/defi/dex/protocols/{protocolId}/lens"
AMBERDATA_DEFI_REST_DEX_POOL_LENS_ENDPOINT = "https://api.amberdata.com/defi/dex/protocols/{protocolId}/pools/{poolAddress}/lens"
AMBERDATA_DEFI_REST_DEX_WALLET_LENS_ENDPOINT = "https://api.amberdata.com/defi/dex/protocols/{protocolId}/wallets/{walletAddress}/lens"

# DEX Trades Endpoints
AMBERDATA_DEFI_REST_DEX_TRADES_HISTORICAL_ENDPOINT = "https://api.amberdata.com/defi/dex/trades"
AMBERDATA_DEFI_REST_DEX_PROTOCOLS_INFORMATION_ENDPOINT = "https://api.amberdata.com/defi/dex/protocols/information"

class MarketDataVenue(str, Enum):
    BINANCE = "binance"
    BINANCEUS = "binanceus"
    BITFINEX = "bitfinex"
    BITGET = "bitget"
    BITHUMB = "bithumb"
    BITMEX = "bitmex"
    BITSTAMP = "bitstamp"
    BYBIT = "bybit"
    CBOEDIGITAL = "cboedigital"
    COINBASE = "gdax"
    GDAX = "gdax"
    GEMINI = "gemini"
    HUOBI = "huobi"
    ITBIT = "itbit"
    KRAKEN = "kraken"
    LMAX = "lmax"
    MERCADOBITCOIN = "mercadobitcoin"
    MEXC = "mexc"
    OKEX = "okex"
    POLONIEX = "poloniex"
    DERIBIT = "deribit"

class TimeFormat(Enum):
    MILLISECONDS = "milliseconds"
    MS = "ms"
    ISO = "iso"
    ISO8601 = "iso8601"
    HR = "hr"
    HUMAN_READABLE = "human_readable"

class DexDataVenue(str, Enum):
    UNISWAP_V2 = "uniswapv2"
    UNISWAP_V3 = "uniswapv3"
    SUSHISWAP = "sushiswap"
    BALANCER_VAULT = "balancer vault"
    CURVE_V1 = "curvev1"
    PANCAKESWAP = "Pancake LPs"
    CRODEFISWAP = "CroDefiSwap"

class LendingProtocol(str, Enum):
    AAVE_V1 = "aave_v1"
    AAVE_V2 = "aave_v2"
    AAVE_V3 = "aave_v3"
    COMPOUND_V1 = "compound_v1"
    CREAM_V1 = "cream_v1"
    CREAM_V2 = "cream_v2"
    FORTUBE_V1 = "fortube_v1"


class TimeInterval(Enum):
    MINUTE = 'minutes'
    HOUR = 'hours'
    DAY = 'days'
    TICKS = 'ticks'


class BatchPeriod(Enum):
    HOUR_1 = timedelta(hours=1)
    HOUR_2 = timedelta(hours=2)
    HOUR_4 = timedelta(hours=4)
    HOUR_8 = timedelta(hours=8)
    HOUR_12 = timedelta(hours=12)
    HOUR_16 = timedelta(hours=16)
    HOUR_20 = timedelta(hours=20)
    DAY_1 = timedelta(days=1)
    DAY_3 = timedelta(days=3)
    DAY_7 = timedelta(days=7)

class TimeBucket(Enum):
    MINUTES_5 = '5m'
    HOURS_1 = '1h'
    DAYS_1 = '1d'

class SortBy(Enum):
    NAME = 'name'
    NUMPAIRS = 'numPairs'

class SortDirection(Enum):
    ASCENDING = 'asc'
    DESCENDING = 'desc'

class DailyTime(Enum):
    T1600_M0500 = "T16:00-05:00"
    T1600_M0400 = "T16:00-04:00"
    T1600_P0000 = "T16:00+00:00"
    T1600_P0100 = "T16:00+01:00"
    T1600_P0400 = "T16:00+04:00"
    T1600_P0800 = "T16:00+08:00"
    T1600_P0900 = "T16:00+09:00"

class Blockchain(Enum):
    ETHEREUM_MAINNET = 'ethereum-mainnet'
    POLYGON_MAINNET = 'polygon-mainnet'
    AVALANCHE_MAINNET = 'avalanche-mainnet'
    ARBITRUM_ONE_MAINNET = 'arb-one-mainnet'
    BITCOIN_MAINNET = 'bitcoin-mainnet'
    BITCOIN_CASH_MAINNET = 'bitcoin-abc-mainnet'
    BINANCE_SMART_CHAIN_MAINNET = 'bnb-mainnet'
    LITECOIN_MAINNET = 'litecoin-mainnet'
    SOLANA_MAINNET = 'solana-mainnet'

class DEXSortBy(Enum):
    NUM_PAIRS = 'numPairs'
    VOLUME_USD = 'volumeUSD'
    # Add other sorting options as needed
