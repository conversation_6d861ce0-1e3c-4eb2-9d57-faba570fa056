import os
import unittest
from datetime import datetime, timedelta

import numpy
import pandas as pd
import pytz

from common import ApiKeyGetMode
from constants import MarketDataVenue, TimeFormat, TimeInterval
from swaps.service import SwapsRestService

# Determine the directory of the current file
current_dir = os.path.dirname(__file__)
local_key_path = os.path.join(current_dir, "../../.localKeys")

srs = SwapsRestService(ApiKeyGetMode.LOCAL_FILE, {"local_key_path": local_key_path}, 32)


class SwapsRestTest(unittest.TestCase):

    def test_headers(self):
        """Test that headers are correctly set with API key and accept headers."""
        headers = srs._headers()
        self.assertTrue('x-api-key' in headers.keys(), 'x-api-key not in headers')
        self.assertTrue('accept' in headers.keys(), 'accept not in headers')
        self.assertTrue(headers['accept'] == 'application/json', 'accept != application/json')

    def test_get_funding_batch_historical_raw(self):
        """Test getting raw funding rate batch historical data."""
        exchange = MarketDataVenue.BINANCE
        instruments = ["BTCUSDT", "ETHUSDT"]
        
        # Test with default parameters
        funding_data = srs.get_funding_batch_historical_raw(exchange, instruments)
        self.assertIsNotNone(funding_data, 'No funding data returned')
        self.assertGreater(len(funding_data), 0, 'Empty funding data returned')
        
        # Check for expected columns
        expected_columns = ['instrument', 'timestamp', 'fundingRate']
        for col in expected_columns:
            self.assertIn(col, funding_data.columns, f'{col} not in funding data columns')
        
        # Check that requested instruments are in the data
        instruments_in_data = funding_data['instrument'].unique()
        for instrument in instruments:
            self.assertIn(instrument, instruments_in_data, f'{instrument} not in returned data')

    def test_get_funding_batch_historical(self):
        """Test getting indexed funding rate batch historical data."""
        exchange = MarketDataVenue.BINANCE
        instruments = ["BTCUSDT", "ETHUSDT"]
        
        # Test with default parameters
        funding_data = srs.get_funding_batch_historical(exchange, instruments)
        self.assertIsNotNone(funding_data, 'No funding data returned')
        self.assertGreater(len(funding_data), 0, 'Empty funding data returned')
        
        # Check that index is set correctly
        self.assertEqual(funding_data.index.name, 'instrument', 'Index not set to instrument')
        
        # Check that requested instruments are in the index
        instruments_in_index = funding_data.index.unique()
        for instrument in instruments:
            self.assertIn(instrument, instruments_in_index, f'{instrument} not in index')
        
        # Check for expected columns
        expected_columns = ['timestamp', 'fundingRate']
        for col in expected_columns:
            self.assertIn(col, funding_data.columns, f'{col} not in funding data columns')

    def test_get_funding_batch_historical_with_time_range(self):
        """Test getting funding rate batch historical data with time range parameters."""
        exchange = MarketDataVenue.BINANCE
        instruments = ["BTCUSDT", "ETHUSDT"]
        
        # Set time range for the past day
        utc_tz = pytz.UTC
        end_date = datetime.now(utc_tz)
        start_date = end_date - timedelta(days=1)
        
        # Test with time range parameters
        funding_data = srs.get_funding_batch_historical_raw(
            exchange, 
            instruments, 
            start_date=start_date, 
            end_date=end_date,
            time_format=TimeFormat.ISO
        )
        
        self.assertIsNotNone(funding_data, 'No funding data returned with time range')
        
        # If data is returned, check that timestamps are within the specified range
        if len(funding_data) > 0:
            funding_data['timestamp'] = pd.to_datetime(funding_data['timestamp'])
            min_timestamp = funding_data['timestamp'].min()
            max_timestamp = funding_data['timestamp'].max()
            
            self.assertGreaterEqual(min_timestamp, start_date, 'Data returned before start date')
            self.assertLessEqual(max_timestamp, end_date, 'Data returned after end date')

    def test_get_ohlcv_batch_raw(self):
        """Test getting raw OHLCV batch data."""
        exchanges = [MarketDataVenue.BINANCE, MarketDataVenue.BYBIT]
        instruments = ["BTCUSDT", "ETHUSDT"]
        
        # Test with default parameters
        ohlcv_data = srs.get_ohlcv_batch_raw(exchanges, instruments)
        self.assertIsNotNone(ohlcv_data, 'No OHLCV data returned')
        
        # Check for expected columns
        expected_columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'exchange', 'instrument']
        for col in expected_columns:
            self.assertIn(col, ohlcv_data.columns, f'{col} not in OHLCV data columns')
        
        # Check that requested exchanges and instruments are in the data
        if len(ohlcv_data) > 0:
            exchanges_in_data = ohlcv_data['exchange'].unique()
            instruments_in_data = ohlcv_data['instrument'].unique()
            
            for exchange in exchanges:
                self.assertIn(exchange.value, exchanges_in_data, f'{exchange.value} not in returned data')
            
            for instrument in instruments:
                self.assertIn(instrument, instruments_in_data, f'{instrument} not in returned data')

    def test_get_ohlcv_batch(self):
        """Test getting indexed OHLCV batch data."""
        exchanges = [MarketDataVenue.BINANCE, MarketDataVenue.BYBIT]
        instruments = ["BTCUSDT", "ETHUSDT"]
        
        # Test with default parameters
        ohlcv_data = srs.get_ohlcv_batch(exchanges, instruments)
        self.assertIsNotNone(ohlcv_data, 'No OHLCV data returned')
        
        # Check that index is set correctly
        self.assertEqual(ohlcv_data.index.name, 'exchange', 'Index not set to exchange')
        
        # Check for expected columns
        expected_columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'instrument']
        for col in expected_columns:
            self.assertIn(col, ohlcv_data.columns, f'{col} not in OHLCV data columns')
        
        # Check that requested exchanges are in the index
        if len(ohlcv_data) > 0:
            exchanges_in_index = ohlcv_data.index.unique()
            for exchange in exchanges:
                self.assertIn(exchange.value, exchanges_in_index, f'{exchange.value} not in index')

    def test_get_ohlcv_batch_with_time_range(self):
        """Test getting OHLCV batch data with time range parameters."""
        exchanges = [MarketDataVenue.BINANCE]
        instruments = ["BTCUSDT"]
        
        # Set time range for the past day
        utc_tz = pytz.UTC
        end_date = datetime.now(utc_tz)
        start_date = end_date - timedelta(days=1)
        
        # Test with time range parameters
        ohlcv_data = srs.get_ohlcv_batch_raw(
            exchanges, 
            instruments, 
            start_date=start_date, 
            end_date=end_date,
            time_interval=TimeInterval.HOUR,
            time_format=TimeFormat.ISO
        )
        
        self.assertIsNotNone(ohlcv_data, 'No OHLCV data returned with time range')
        
        # If data is returned, check that timestamps are within the specified range
        if len(ohlcv_data) > 0:
            ohlcv_data['timestamp'] = pd.to_datetime(ohlcv_data['timestamp'])
            min_timestamp = ohlcv_data['timestamp'].min()
            max_timestamp = ohlcv_data['timestamp'].max()
            
            self.assertGreaterEqual(min_timestamp, start_date, 'Data returned before start date')
            self.assertLessEqual(max_timestamp, end_date, 'Data returned after end date')

    def test_get_open_interest_batch_raw(self):
        """Test getting raw open interest batch data."""
        exchanges = [MarketDataVenue.BINANCE, MarketDataVenue.BYBIT]
        instruments = ["BTCUSDT", "ETHUSDT"]
        
        # Test with default parameters
        oi_data = srs.get_open_interest_batch_raw(exchanges, instruments)
        self.assertIsNotNone(oi_data, 'No open interest data returned')
        
        # Check for expected columns
        expected_columns = ['timestamp', 'type', 'value', 'exchange', 'instrument']
        for col in expected_columns:
            self.assertIn(col, oi_data.columns, f'{col} not in open interest data columns')
        
        # Check that requested exchanges and instruments are in the data
        if len(oi_data) > 0:
            exchanges_in_data = oi_data['exchange'].unique()
            instruments_in_data = oi_data['instrument'].unique()
            
            for exchange in exchanges:
                self.assertIn(exchange.value, exchanges_in_data, f'{exchange.value} not in returned data')
            
            for instrument in instruments:
                self.assertIn(instrument, instruments_in_data, f'{instrument} not in returned data')

    def test_get_open_interest_batch(self):
        """Test getting indexed open interest batch data."""
        exchanges = [MarketDataVenue.BINANCE, MarketDataVenue.BYBIT]
        instruments = ["BTCUSDT", "ETHUSDT"]
        
        # Test with default parameters
        oi_data = srs.get_open_interest_batch(exchanges, instruments)
        self.assertIsNotNone(oi_data, 'No open interest data returned')
        
        # Check that index is set correctly
        self.assertEqual(oi_data.index.name, 'exchange', 'Index not set to exchange')
        
        # Check for expected columns
        expected_columns = ['timestamp', 'type', 'value', 'instrument']
        for col in expected_columns:
            self.assertIn(col, oi_data.columns, f'{col} not in open interest data columns')
        
        # Check that requested exchanges are in the index
        if len(oi_data) > 0:
            exchanges_in_index = oi_data.index.unique()
            for exchange in exchanges:
                self.assertIn(exchange.value, exchanges_in_index, f'{exchange.value} not in index')

    def test_get_open_interest_batch_with_time_range(self):
        """Test getting open interest batch data with time range parameters."""
        exchanges = [MarketDataVenue.BINANCE]
        instruments = ["BTCUSDT"]
        
        # Set time range for the past day
        utc_tz = pytz.UTC
        end_date = datetime.now(utc_tz)
        start_date = end_date - timedelta(days=1)
        
        # Test with time range parameters
        oi_data = srs.get_open_interest_batch_raw(
            exchanges, 
            instruments, 
            start_date=start_date, 
            end_date=end_date,
            time_interval=TimeInterval.HOUR,
            time_format=TimeFormat.ISO
        )
        
        self.assertIsNotNone(oi_data, 'No open interest data returned with time range')
        
        # If data is returned, check that timestamps are within the specified range
        if len(oi_data) > 0:
            oi_data['timestamp'] = pd.to_datetime(oi_data['timestamp'])
            min_timestamp = oi_data['timestamp'].min()
            max_timestamp = oi_data['timestamp'].max()
            
            self.assertGreaterEqual(min_timestamp, start_date, 'Data returned before start date')
            self.assertLessEqual(max_timestamp, end_date, 'Data returned after end date')

    def test_custom_index_keys(self):
        """Test using custom index keys for the returned DataFrames."""
        exchange = MarketDataVenue.BINANCE
        instruments = ["BTCUSDT", "ETHUSDT"]
        
        # Test funding rates with custom index
        custom_index = ['instrument', 'timestamp']
        funding_data = srs.get_funding_batch_historical(
            exchange, 
            instruments, 
            index_keys=custom_index
        )
        
        # Check that index is set correctly
        self.assertEqual(funding_data.index.names, custom_index, f'Index not set to {custom_index}')
        
        # Test OHLCV with custom index
        exchanges = [MarketDataVenue.BINANCE]
        custom_index = ['exchange', 'instrument']
        ohlcv_data = srs.get_ohlcv_batch(
            exchanges, 
            instruments, 
            index_keys=custom_index
        )
        
        # Check that index is set correctly
        self.assertEqual(ohlcv_data.index.names, custom_index, f'Index not set to {custom_index}')
        
        # Test open interest with custom index
        custom_index = ['exchange', 'instrument', 'type']
        oi_data = srs.get_open_interest_batch(
            exchanges, 
            instruments, 
            index_keys=custom_index
        )
        
        # Check that index is set correctly
        self.assertEqual(oi_data.index.names, custom_index, f'Index not set to {custom_index}')


if __name__ == '__main__':
    unittest.main()
