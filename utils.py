import functools
import pandas as pd
from typing import List, Callable, Any


def convert_timestamp(func: Callable) -> Callable:
    """
    Decorator to convert timestamp columns to datetime format in DataFrame results.

    This decorator should be applied to processed functions (non-raw functions) that return
    DataFrames with timestamp columns. It automatically converts timestamp columns to
    datetime format based on the time_format parameter.

    Handles the following columns:
    - timestamp: Converted based on time_format parameter
    - startDate: Always converted using pd.to_datetime()
    - endDate: Always converted using pd.to_datetime()

    Args:
        func: The function to decorate

    Returns:
        Wrapped function that processes timestamps in the result DataFrame
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        # Get the result DataFrame from the original function
        df = func(*args, **kwargs)

        # Check if the result is a DataFrame
        if isinstance(df, pd.DataFrame):
            # Get time_format from kwargs if it exists, otherwise use default
            time_format = kwargs.get('time_format')

            # Convert timestamp column based on time_format
            if 'timestamp' in df.columns:
                if time_format in ['milliseconds', 'ms']:
                    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                elif time_format in ['iso', 'iso8601']:
                    df['timestamp'] = pd.to_datetime(df['timestamp'])
                else:
                    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')

            # Convert startDate and endDate columns (these are typically already in ISO format)
            if 'startDate' in df.columns:
                df['startDate'] = pd.to_datetime(df['startDate'])
            if 'endDate' in df.columns:
                df['endDate'] = pd.to_datetime(df['endDate'])

        return df

    return wrapper
