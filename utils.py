import functools
import pandas as pd
from typing import List, Callable, Any


def convert_timestamp(func: Callable) -> Callable:
    """
    Decorator to convert timestamp columns to datetime format in DataFrame results.
    
    This decorator should be applied to processed functions (non-raw functions) that return
    DataFrames with timestamp columns. It automatically converts timestamp columns to
    datetime format based on the time_format parameter.
    
    Args:
        func: The function to decorate
        
    Returns:
        Wrapped function that processes timestamps in the result DataFrame
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        # Get the result DataFrame from the original function
        df = func(*args, **kwargs)
        
        # Check if the result is a DataFrame and has a timestamp column
        if isinstance(df, pd.DataFrame) and 'timestamp' in df.columns:
            # Get time_format from kwargs if it exists, otherwise use default
            time_format = kwargs.get('time_format')
            
            # Convert timestamp to datetime based on time_format
            if time_format in ['milliseconds', 'ms']:
                df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            elif time_format in ['iso', 'iso8601']:
                df['timestamp'] = pd.to_datetime(df['timestamp'])
            else:
                df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                
        return df
    
    return wrapper
